<template>
	<view class="box">
		<u-navbar :is-back="true" leftIconColor="#000000" :autoBack="true" :bgColor="'transparent'">
			<view slot="left">
				<img src="/static/wback.png" style="width: 50rpx;height: 50rpx;"></img>
			</view>
		</u-navbar>
		<view style="position: relative;">
			<!--轮播-->
			<view class="swiper-container swiper-mask">
				<swiper :circular="true" :autoplay="true" :interval="3000" :duration="500" :indicator-dots="false"
					style="width: 100%; height: 400rpx;">
					<swiper-item v-for="(item, index) in swiperList" :key="index">
						<image :src="item.image" style="width: 100%; height: 100%; border-radius: 12rpx;"
							mode="scaleToFill"></image>
					</swiper-item>
				</swiper>
			</view>
		</view>
		<view style="padding: 30rpx;">
			<view style="display: flex;align-items: center;">
				<view>
					<image style="width: 120rpx;height: 120rpx;border: 4rpx #ffffff solid;border-radius: 23rpx;"
						src="https://qingchunta.hschool.com.cn/uploads/20250814/1eaa6355733b214156d6a57b911bcb42.jpg">
					</image>
				</view>
				<view style="padding-left:20rpx;">
					<view style="font-size: 36rpx;color: #FFFFFF;font-weight: 600;">猎鹰户外训练俱乐部</view>
					<view style="font-size: 28rpx;color: #FFFFFF;font-weight: 400;margin-top: 25rpx;">
						<text>举办过14场活动</text>
						<text style="margin: 0rpx 30rpx;">•</text>
						<text>2545人参与过</text>
					</view>
				</view>
			</view>
			<view
				style="background-color: rgba(255,255,255,0.2);min-height: 325rpx;padding: 30rpx;border-radius: 24rpx;position: relative;margin-top: 40rpx;">
				<image class="noImg" src="https://naweigetetest2.hschool.com.cn/dyqc/club_t.png" style="width: 240rpx;"
					mode="widthFix"></image>
				<image class="noImg" src="https://naweigetetest2.hschool.com.cn/dyqc/club_r.png"
					style="width: 85rpx;position: absolute;right: 30rpx;top: -20rpx;" mode="widthFix"></image>
				<view>
					<view class="third-center1" ref="richTextContainer" v-if="!showToggleButtons" style="height: auto;">
						<view class="v_html">
							<rich-text style="white-space: pre-line;" :nodes="content"></rich-text>
						</view>
					</view>
					<view class="third-center" v-if="showToggleButtons"
						:style="{ height: richTextShow ? 'auto' : '95px', overflow: 'hidden', margin: '0 auto', paddingBottom: '0' }"
						ref="richTextContainer">
						<view class="v_html">
							<rich-text style="white-space: pre-line;" :nodes="content"></rich-text>
						</view>
					</view>
					<view style="height: 1px;width: 100%;background-color: #FFFFFF;opacity: 0.2;margin: 30rpx 0rpx;">
					</view>
					<view>
						<view v-if="!richTextShow" @click="richTextShow = true"
							style="color: #9996AB;font-size: 30rpx;text-align: center;">
							<view style="vertical-align: middle;display: inline-block;margin-right: 20rpx;">展开全部</view>
							<image src="/static/xia.png" style="width: 30rpx;height: 30rpx;vertical-align: middle;">
							</image>
						</view>
						<view v-if="richTextShow" @click="richTextShow = false"
							style="color: #9996AB;font-size: 30rpx;text-align: center;">
							<view style="vertical-align: middle;display: inline-block;margin-right: 20rpx;">收起介绍</view>
							<image src="/static/shang.png" style="width: 30rpx;height: 30rpx;vertical-align: middle;">
							</image>
						</view>
					</view>
				</view>
			</view>
		</view>
		<view class="jxz">
123123
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			richTextShow: false,
			showToggleButtons: false, // 控制是否显示展开/收起按钮
			// 轮播图数据
			swiperList: [
				{
					id: 1,
					image: 'https://dummyimage.com/800x500/000/fff',
					title: '轮播图1'
				},
				{
					id: 2,
					image: 'https://dummyimage.com/800x500/000/fff',
					title: '轮播图2'
				},
				{
					id: 3,
					image: 'https://dummyimage.com/800x500/000/fff',
					title: '轮播图3'
				}
			],
			content: `车行山水，全称为：河南省车行山水户外运动俱乐部。是经河南省民政厅正式批准成立的正规户外自驾游俱乐部。自2003年成立以来，22年间坚持每周末组织自驾游或会员出行活动，车轮已走遍祖国各地。共计组织各类大小活动1000多次，参加活动的会员已超过200000人次，目前会员车辆8000余台，涉及人数超过50000人。

车行山水户外俱乐部是一个由洛阳自驾车旅游爱好者自愿参与、接受政府有关机构指导和各界支持的社会团体。活动内容主要为自驾车旅游、徒步穿越等。宗旨为融入自然，品味生活。遵循的原则是平等尊重、健康积极、友善互助。主张“两本两乐”，即以人为本，以爱为本，以奉献为乐，以众乐为乐。

1、每周末组织洛阳周边一或两日短途自驾游，或会员集会、聚餐等活动。

2、每月组织一周以上中长途自驾游活动1-3次。

3、每年组织远途如西藏、新疆以及境外自驾游活动3-5次。

4、每年举办大型综合娱乐类活动1-3次。

5、每年组织俱乐部大型年庆活动及歌舞晚会1次。

6、每年不定期组织爱心慈善类活动1-3次。

`,
		}
	},
	mounted() {
		this.checkContentHeight();
	},
	updated() {
		this.checkContentHeight();
	},
	methods: {
		checkContentHeight() {
			// 使用 uni.createSelectorQuery 获取富文本容器的高度
			const query = uni.createSelectorQuery().in(this);
			query.select('.v_html').boundingClientRect(data => {
				if (data && data.height > 95) { // 300px 是容器的固定高度
					this.showToggleButtons = true;
				} else {
					this.showToggleButtons = false;
				}
			}).exec();
		},
	}
}
</script>

<style scoped>
page {}

.box {
	background-color: rgb(16, 8, 58);
	min-height: 100vh;
	background-image: url("https://naweigetetest2.hschool.com.cn/dyqc/club_bg.png");
	background-size: 100%;
	background-repeat: no-repeat;
	font-family: PingFang SC, PingFang SC;
}

.jxz {
	background-image: url("https://naweigetetest2.hschool.com.cn/dyqc/club_d.png");
	background-size: 100%;
}

.swiper-container {
	width: 100%;
	margin-bottom: 20rpx;
}

/* 使用mask-image实现底部虚化效果 */
.swiper-mask {
	-webkit-mask-image: linear-gradient(to bottom,
			rgba(0, 0, 0, 1) 0%,
			rgba(0, 0, 0, 1) 60%,
			rgba(0, 0, 0, 0.7) 75%,
			rgba(0, 0, 0, 0.3) 90%,
			rgba(0, 0, 0, 0) 100%);
	mask-image: linear-gradient(to bottom,
			rgba(0, 0, 0, 1) 0%,
			rgba(0, 0, 0, 1) 60%,
			rgba(0, 0, 0, 0.7) 75%,
			rgba(0, 0, 0, 0.3) 90%,
			rgba(0, 0, 0, 0) 100%);
}

.v_html {
	font-size: 30rpx;
	color: #FFFFFF;
	line-height: 45rpx;
}
</style>
