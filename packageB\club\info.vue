<template>
	<view class="box">
		<u-navbar :is-back="true" leftIconColor="#000000" :autoBack="true" :bgColor="'transparent'">
			<view slot="left">
				<img src="/static/wback.png" style="width: 50rpx;height: 50rpx;"></img>
			</view>
		</u-navbar>
		<view style="position: relative;">
			<!--轮播-->
			<view class="swiper-container">
				<swiper :circular="true" :autoplay="true" :interval="3000" :duration="500" :indicator-dots="false"
					style="width: 100%; height: 450rpx;">
					<swiper-item v-for="(item, index) in swiperList" :key="index">
						<image :src="item.image"
							style="width: 100%; height: 100%; border-radius: 12rpx;" mode="scaleToFill"></image>
					</swiper-item>
				</swiper>
			</view>
			<!-- 底部渐变虚化效果 -->
			<!-- <view
				style="position: absolute;bottom: 0;width: 100%;height: 120rpx;background-image: linear-gradient(to top, rgba(16, 8, 58, 0.9) 0%, rgba(16, 8, 58, 0.6) 30%, rgba(16, 8, 58, 0.3) 60%, rgba(16, 8, 58, 0) 100%);pointer-events: none;">
			</view> -->
		</view>
		<view class="box">

		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			// 轮播图数据
			swiperList: [
				{
					id: 1,
					image: 'https://dummyimage.com/800x500/000/fff',
					title: '轮播图1'
				},
				{
					id: 2,
					image: 'https://dummyimage.com/800x500/000/fff',
					title: '轮播图2'
				},
				{
					id: 3,
					image: 'https://dummyimage.com/800x500/000/fff',
					title: '轮播图3'
				}
			]
		}
	},
	methods: {

	}
}
</script>

<style scoped>
page {}

.box {
	background-color: rgb(16, 8, 58);
	height: 100vh;
	background-image: url("https://naweigetetest2.hschool.com.cn/dyqc/club_bg.png");
	background-size: 100%;
	background-repeat: no-repeat;
}

.swiper-container {
	width: 100%;
	margin-bottom: 20rpx;
}


</style>
