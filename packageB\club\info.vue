<template>
	<view class="box">
		<u-navbar :is-back="true" leftIconColor="#000000" :autoBack="true" :bgColor="'transparent'">
			<view slot="left">
				<img src="/static/wback.png" style="width: 50rpx;height: 50rpx;"></img>
			</view>
		</u-navbar>
		<view style="position: relative;">
			<!--轮播-->
			<view class="swiper-container">
				<swiper :circular="true" :autoplay="true" :interval="3000" :duration="500" :indicator-dots="false"
					style="width: 100%; height: 450rpx;">
					<swiper-item v-for="(item, index) in swiperList" :key="index">
						<image class="blur-gradient" :src="item.image"
							style="width: 100%; height: 100%; border-radius: 12rpx;" mode="scaleToFill"></image>
					</swiper-item>
				</swiper>
			</view>
			<!-- 底部虚化效果 -->
			<!-- <view style="position: absolute;bottom: -60rpx;width: 100%;">
				<view
					style="width: 100%;height: 60rpx;background-image: linear-gradient(to top, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.6) 40%, rgba(255, 255, 255, 0) 100%);pointer-events: none;">
				</view>
				<view
					style="width: 100%;height: 60rpx;background-image: linear-gradient(to top, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.6) 40%, rgba(255, 255, 255, 0.9) 100%);pointer-events: none;">
				</view>
			</view> -->
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			// 轮播图数据
			swiperList: [
				{
					id: 1,
					image: 'https://dummyimage.com/800x500/000/fff',
					title: '轮播图1'
				},
				{
					id: 2,
					image: 'https://dummyimage.com/800x500/000/fff',
					title: '轮播图2'
				},
				{
					id: 3,
					image: 'https://dummyimage.com/800x500/000/fff',
					title: '轮播图3'
				}
			]
		}
	},
	methods: {

	}
}
</script>

<style scoped>
page {}

.box {
	background-color: rgb(16, 8, 58);
	height: 100vh;
	background-image: url("https://naweigetetest2.hschool.com.cn/dyqc/club_bg.png");
	background-size: 100%;
	background-repeat: no-repeat;
}

.swiper-container {
	width: 100%;
	margin-bottom: 20rpx;
}

.blur-gradient::before {
	content: "";
	position: absolute;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	background: linear-gradient(to bottom, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 1) 100%);
	backdrop-filter: blur(0px) 0%, blur(20px) 100%;
}
</style>
